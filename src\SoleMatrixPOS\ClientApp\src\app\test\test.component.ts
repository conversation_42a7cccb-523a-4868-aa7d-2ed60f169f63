import { Component, OnInit } from '@angular/core';
import { StockSearchModalComponent } from '../stock-search/stock-search-modal/stock-search-modal.component';
import { StockSaleTableModalComponent } from '../stock-sale-table/stock-sale-table-modal/stock-sale-table-modal.component';
import { CustomerClubModalComponent } from '../customer-club/customer-club-modal/customer-club-modal.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Navigation, NavigationEnd, Router } from '@angular/router';
import { Observable, of, pipe } from 'rxjs';
import { CustomerClubDto, StockItemDto } from '../pos-server.generated';
import { Store } from '@ngrx/store';
import { AppState } from '../reducers';

import * as customerClubSearchSelectors from '../reducers/customer-club/club-search/customer-club.selectors';
import * as cartActions from '../reducers/sales/cart/cart.actions';
import * as cartSelectors from '../reducers/sales/cart/cart.selectors';

import { Stock } from '../stock-sale-table/classes/stock';
import { CartItem } from '../reducers/sales/cart/cart.reducer';
import { debounce, filter } from 'rxjs/operators';
import { UrlHistoryService } from '../url-history.service';

@Component({
	selector: 'pos-test',
	templateUrl: './test.component.html',
	styleUrls: ['./test.component.scss'],
	host: { class: 'wrapper' }
})
export class TestComponent implements OnInit {
	collapsed = true;

	private selectedCustomerClubMember$: Observable<CustomerClubDto>;
	public selectedCustomerClubMember: CustomerClubDto = null;

	private timeout: any;

	public cart: CartItem[] = [];
	public cart$: Observable<CartItem[]>;
	private persistentCart: Observable<CartItem[]>;

	public total: number = 0;
	public total$: Observable<number>;

	constructor(
		private modalService: NgbModal, 
		private router: Router, 
		private store: Store<AppState>,
		private urlHistoryService: UrlHistoryService) {
		
			if(urlHistoryService.lastUrl === '/home'){

				this.store.dispatch(cartActions.init());
			}

	}

	ngOnInit() {
		this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
		this.selectedCustomerClubMember$.subscribe((s) => { this.selectedCustomerClubMember = s });

		
		this.cart$ = of(this.cart);
		this.total$ = this.store.select(cartSelectors.total);// of(this.total);
		this.persistentCart = this.store.select(cartSelectors.cart);
	
		this.persistentCart.subscribe((v) => {
			console.log("persistentCart", v);
			let cartItems = v as CartItem[];
			console.log("ci", cartItems);
			if (this.cart.length == 0) {
				cartItems.forEach(persistentCartItem => {
					this.cart.push( Object.assign({}, persistentCartItem) as CartItem);
				});
			} else {
				cartItems.forEach(persistentCartItem => {
					console.log("PERSISTENT CART", cartItems);
					console.log("MUTABLE CART", this.cart);
					let found = this.cart.find(
						(mutableCartItem, i, obj) => {
							return mutableCartItem.stockItem.styleCode == persistentCartItem.stockItem.styleCode;
						}
					);
					console.log("found", found);

					if (found != undefined) {
						console.log("bestValue FOUND",persistentCartItem);
						found.bestValue = persistentCartItem.bestValue;
					}
				});
			}
		}

		);

		this.cart$.subscribe(c => {
			console.log("CART", c);
		});

	}

	openSearch() {
		const modalRef = this.modalService.open(StockSearchModalComponent, { size: 'xl', centered: true });
		modalRef.componentInstance.name = 'ProductSearch';
		modalRef.result.then((result) => {
			if (result) {
				this.addItem(result as StockItemDto);
			}
		}).catch((reason) => console.log(reason));
	}

	openStockSaleTable() {
		const modalRef = this.modalService.open(StockSaleTableModalComponent, { size: 'xl', centered: true });
		modalRef.componentInstance.name = 'StackSaleTable';
		modalRef.result.then((result) => {
			if (result) {
				let val: StockItemDto = result;
			}
		});
	}

	goHome() {
		this.router.navigateByUrl('/home');
	}

	launchCustomerClubModal() {
		const modalRef = this.modalService.open(CustomerClubModalComponent, { size: 'xl', centered: true });
		modalRef.componentInstance.name = 'CustomerClubModal';
		modalRef.result.then((result) => {
			if (result) {
				console.log('result from modal:', result);
			}
		});
	}

	private addItem(item: StockItemDto) {	
		this.cart.push({quantity: 1, stockItem: item, bestValue: item.price} as CartItem);
		this.store.dispatch(cartActions.addItem({stockItem: item }));
	}

	private removeItem(item: StockItemDto) {
		let cartItemToRemove: CartItem | undefined;
		this.cart.forEach((v,i)=>{
			if(v.stockItem == item) {
				cartItemToRemove = v;
				this.cart.splice(i,1);
			}
		});
		if (cartItemToRemove) {
			this.store.dispatch(cartActions.removeItem({ id: cartItemToRemove.id, stockItem: cartItemToRemove.stockItem }));
		}
	}

	private setNumItems(item: StockItemDto, quantity: number) {	
		if(quantity <= 0){
			this.removeItem(item);
		} else {
			this.cart.forEach((v, i) => {
				if (v.stockItem == item) {
					this.cart[i].quantity = quantity;
				}
			});
		}
		this.store.dispatch(cartActions.setNumberOfItems({quantity: quantity, stockItem: item}));
	}

	clickCount: number = 0;

	removeItemOnDoubleClick(params: StockItemDto) {
		++this.clickCount;
		setTimeout(()=>{
			if(this.clickCount === 1){

			} else if (this.clickCount === 2) {
				this.removeItem(params);
			}
			this.clickCount = 0;
		}, 250);
	}

	setNumberOfItems(item: StockItemDto, quantity: number) {
		this.setNumItems(item, quantity);
		//clearTimeout(this.timeout);
		//this.timeout = setTimeout(()=>{this.setNumItems(item, quantity)},1000);
	}

}