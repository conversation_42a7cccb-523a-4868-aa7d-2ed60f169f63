import { Component, OnInit, OnDestroy } from '@angular/core';
import { select, Store } from '@ngrx/store';
import { Observable, Subject, Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { PrintingService } from 'src/app/printing/printing.service';
import Swal from 'sweetalert2';
import * as receiptActions from 'src/app/reducers/receipt-printing/receipt.actions';
import { StockItemDto, CustomerClubDto, CreateOrderDto, CustOrderHeaderDTO, CustOrderLineDTO, TransactionDto, TranslogDto, CreateOrderRequest, QuoteHeaderDTO, QuoteLineDTO, ReceiptTransactionDto } from 'src/app/pos-server.generated';
import * as cartActions from 'src/app/reducers/sales/cart/cart.actions';
import * as cartSelectors from 'src/app/reducers/sales/cart/cart.selectors';
import { Payment, PaymentType, Transaction } from 'src/app/payment/payment.service';
import * as transActions from 'src/app/reducers/transaction/transaction.actions';
import { cartItemToTranslog } from 'src/app/reducers/sales/cart/cart.reducer';
import * as SysConfigActions from 'src/app/reducers/sys-config/sys-config.actions';
import * as SysConfigSelectors from 'src/app/reducers/sys-config/sys-config.selectors';
import * as customerClubSearchSelectors from 'src/app/reducers/customer-club/club-search/customer-club.selectors';
import * as quoteActions from 'src/app/reducers/quote-item/quote.actions';
import * as transSelectors from 'src/app/reducers/transaction/transaction.selectors';
import { takeUntil, tap } from 'rxjs/operators';
import { CartItem } from 'src/app/reducers/sales/cart/cart.reducer';
import * as staffActions from 'src/app/reducers/staff/staff.actions';
import { AppState } from 'src/app/reducers';
import { UrlHistoryService } from 'src/app/url-history.service';
import { QuoteClient } from 'src/app/pos-server.generated';
import { SolemateReceiptOptions } from 'src/app/printing/printing.service';
import { EmailReceiptComponent } from 'src/app/email-receipt/email-receipt.component';
import { selectQuoteNo } from 'src/app/reducers/quote-item/quote.selectors';

@Component({
  selector: 'pos-customer-quote',
  templateUrl: './customer-quote.component.html',
  styleUrls: ['./customer-quote.component.scss']
})

export class CustomerQuoteComponent implements OnInit, OnDestroy {

  selectedCustomerClubMember$: Observable<CustomerClubDto>;
  transaction: Transaction; // Transaction to handle payments
  transaction$: Observable<Transaction>;
  cart$: Observable<CartItem[]>;
  quoteNo$: Observable<string>;
  quoteNo: string;
  cart: CartItem[] = [];
  selectedCustomerClubMember: CustomerClubDto = null;
  totalValueOfItems: number = 0;
  transNo: number = 0;
  readyToProcess: boolean = false;
  private destroy$ = new Subject<void>();
  private client: QuoteClient;

  private subscriptions: Subscription = new Subscription();

  constructor(
    private router: Router,
    private store: Store<AppState>,
    private modalService: NgbModal,
    private urlHistory: UrlHistoryService,
    private printService: PrintingService
  ) {
    this.transaction = new Transaction(0);
   }

  ngOnInit() {

    this.store.dispatch(transActions.getTransactionNo());
    this.store.select(transSelectors.transNo)
      .pipe(
        tap(transNo => {
          this.transNo = transNo;
          console.log('Transaction number updated:', transNo);
        })
      )
      .subscribe();

    this.store.dispatch(cartActions.init()); 

    this.store.dispatch(quoteActions.getQuoteNo());
	  this.quoteNo$ = this.store.select(selectQuoteNo);
	  this.quoteNo$.pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (quoteNo) => {
        if (quoteNo) {
          this.quoteNo = quoteNo;
          console.log('Quote Number received:', this.quoteNo);
        } else {
          console.warn('No quote number received');
        }
      },
      error: (error) => {
        console.error('Error getting quote number:', error);
      }
    });

    if (this.urlHistory.previousUrl === '/home') this.initState();

    this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
    const customerSub = this.selectedCustomerClubMember$.subscribe(
      (customerDetails) => {
        if (customerDetails) {
          console.log('Customer details received:', customerDetails);
          this.selectedCustomerClubMember = customerDetails;
        }
      }
    );
    this.subscriptions.add(customerSub);
    this.subscribeToState();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  subscribeToState() {
    this.cart$ = this.store.select(cartSelectors.cart);
    const cartSub = this.cart$.subscribe(cart => {
      this.cart = cart;
      this.calculateTotalValue();
    });
    this.subscriptions.add(cartSub);

    const readySub = this.store.select(cartSelectors.noItems).subscribe(n => this.readyToProcess = (n > 0));
    this.subscriptions.add(readySub);
  }

  initState() {
    this.store.dispatch(cartActions.init());
    this.store.dispatch(transActions.init());
  }

  backBtnClick() {
    console.log("Navigating backwards...");
    this.router.navigateByUrl(this.urlHistory.previousUrl);
  }

  itemLookup(item: StockItemDto) {
    this.addCartItem(item);
  }

  addCartItem(item: StockItemDto) {
    this.store.dispatch(cartActions.addItem({ stockItem: item }));
    console.log(item);
  }

  onTableItemDeleteClicked(itemIndex: number) {
    let cartItem = this.cart[itemIndex];
    this.store.dispatch(cartActions.removeItem({ id: cartItem.id, stockItem: cartItem.stockItem }));
  }

  // Implement orderItemClick method
  quoteItemClick() {
    if (!this.readyToProcess) {
      Swal.fire("Error", "You will need to add at least one item to the cart before ordering.", 'error');
      return;
    }
    // Instead of waiting for transNo, directly proceed with order creation
    const quoteHeader = this.createQuoteHeader();
    const quoteLines = this.creatQuoteLines();
    const transactionDto: TransactionDto = this.getSaleTransaction(); // Assuming you have a method to get this

    // If no deposit required, submit the order and transaction together
    const CreateQuoteDto = {
      quoteDto: {
        quoteHeader: quoteHeader,
        quoteLines: quoteLines
      },
      transactionDto: transactionDto
    };

    // Dispatch the combined action
    this.store.dispatch(quoteActions.createQuote({ payload: CreateQuoteDto }));

    const saleDateTime = new Date();

    let receiptTrans: ReceiptTransactionDto = {
      logs: transactionDto.translogs,
      pays: transactionDto.payments,
      saleDateTime: saleDateTime,
      transType: 12,
    };
    
    Swal.fire({
        title: "Quote Completed",
        text: "The Quote was successfully submitted.",
        showCancelButton: true,
        cancelButtonText: "Email Receipt",
        confirmButtonText: "Print Receipt",
    }).then(async (result) => {
        if (result.value) {
            await this.printService.printQuoteReceipt(
              transactionDto.translogs,
              12,
              this.transNo.toString(),
              SolemateReceiptOptions.default(),
              receiptTrans,
              this.selectedCustomerClubMember.firstname + " " + this.selectedCustomerClubMember.surname,
              this.selectedCustomerClubMember.clientCode,
              quoteHeader.quoteCode
            );
            this.store.dispatch(staffActions.clearStaffLogin());
        } else if (result.dismiss === Swal.DismissReason.cancel) {
            this.openEmailModal(receiptTrans).then(() => {
                this.store.dispatch(staffActions.clearStaffLogin());
            });
        }
        else {
          this.store.dispatch(staffActions.clearStaffLogin());
        }
    });
  }

  openEmailModal(receiptTrans: ReceiptTransactionDto): Promise<void> {
    return new Promise((resolve) => {
      const modalRef = this.modalService.open(EmailReceiptComponent, {
      size: 'lg',
      backdrop: 'static'
      });
    
      // Pass receiptTrans to the EmailReceiptComponent
      modalRef.componentInstance.receiptTrans = receiptTrans;
      modalRef.componentInstance.orderNo = this.quoteNo;
    
      // Check if a customer club member is selected and pass the email
      if (this.selectedCustomerClubMember && this.selectedCustomerClubMember.email) {
      modalRef.componentInstance.customerEmail = this.selectedCustomerClubMember.email;
      }
    
      modalRef.result.then(() => {
      console.log('Email receipt sent.');
      resolve();  // Resolve the promise once the modal is closed
      }).catch(() => {
      resolve();  // Resolve the promise if the modal is dismissed
      });
    });
    }

  // Helper method to create OrderHeader
  private createQuoteHeader(): QuoteHeaderDTO {
    const clientCode = this.selectedCustomerClubMember && this.selectedCustomerClubMember.clientCode ? this.selectedCustomerClubMember.clientCode : "NOCODE";
    const orderStaff = '1'; // Replace with actual staff ID

    const quoteHeader: QuoteHeaderDTO = {
      quoteCode: this.quoteNo, // Server will generate this
      quoteStaff: orderStaff,
      clientCode: clientCode,
      quoteDate: new Date(),
      quoteCancelled: false,
      lastPaymentDate: null,
    };

    return quoteHeader;
  }

  // Helper method to create OrderLines
  private creatQuoteLines(): QuoteLineDTO[] {
    const quoteLines: QuoteLineDTO[] = [];

    let lineNo = 1;
    for (const cartItem of this.cart) {
      const line: QuoteLineDTO = {
        quoteCode: this.quoteNo, // Will be set on the server side
        lineNo: lineNo,
        styleCode: cartItem.stockItem.styleCode,
        colourCode: cartItem.stockItem.colourCode,
        sizeCode: cartItem.stockItem.size,
        quantity: cartItem.quantity,
        sellingPrice: cartItem.bestValue,
        extendedValue: cartItem.bestValue * cartItem.quantity,
      };

      quoteLines.push(line);
      lineNo++;
    }

    return quoteLines;
  }

  // Get the transaction details (stub for the example)
  getSaleTransaction(): TransactionDto {
    this.transaction = new Transaction(0);
		// Get all translogs
		let resTranslogs: TranslogDto[] = [];
		for (let i = 0; i < this.cart.length; i++) {
			let cartItem = this.cart[i];
			let lineNumber = i + 1;
			
			// Check if the customer club member exists and has a clientCode
			let clientCode = (this.selectedCustomerClubMember && this.selectedCustomerClubMember.clientCode)
				? this.selectedCustomerClubMember.clientCode
				: undefined;
	
			resTranslogs.push(cartItemToTranslog(cartItem, lineNumber, this.transNo, clientCode));
		}
	
		// Create the base transaction object
		let transaction: TransactionDto = {
			payments: this.transaction.toTranspayDtos(),
			translogs: resTranslogs,
			transType: 12
		};
    console.log("the transaction", transaction)
		return transaction;
	}		

  private calculateTotalValue() {
    this.totalValueOfItems = this.cart.reduce((total, item) => total + (item.bestValue * item.quantity), 0);
  }

}

