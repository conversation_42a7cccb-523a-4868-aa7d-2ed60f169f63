import {
	<PERSON>mpo<PERSON>,
	<PERSON>ement<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>roy,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	ItemBarcodeSearchRequestDto,
	StockItemDto,
	StockTableItemDto,
	SuspendSaleHdrDto,
    SuspendTransToCartDto,
} from "../pos-server.generated";
import { StockSearchModalComponent } from "../stock-search/stock-search-modal/stock-search-modal.component";

import * as cartActions from "../reducers/sales/cart/cart.actions";
import * as cartSelectors from "../reducers/sales/cart/cart.selectors";
import * as transActions from "../reducers/transaction/transaction.actions";
import * as SuspendSaleActions from "../reducers/suspend-sale/suspend-sale.actions";
import * as suspendSaleSelectors from "../reducers/suspend-sale/suspend-sale.selectors";

import { Store } from "@ngrx/store";
import { AppState } from "../reducers";
import { Observable, Subscription } from "rxjs";
import { Actions, ofType } from "@ngrx/effects";
import { CartItem } from "../reducers/sales/cart/cart.reducer";
import { UrlHistoryService } from "../url-history.service";
import { Router } from "@angular/router";
import { CreateErrorModal } from "../error-modal/error-modal.component";
import { SaleSuspendHdrModalComponent } from "../sales/sale-suspend-hdr-modal/sale-suspend-hdr-modal.component";
import { Console } from "console";
import { SundryModalComponent } from './sundry-modal/sundry-modal.component';

@Component({
	selector: "pos-sales",
	templateUrl: "./sales.component.html",
	styleUrls: ["./sales.component.scss"],
})
export class SalesComponent implements OnInit, OnDestroy {
	suspendSaleHeaders$: Observable<SuspendSaleHdrDto[]>;
	subscription: Subscription;
	modalOpen: boolean = false;
	canEditCart: boolean = true;
	currentSuspendSaleNo: number | null = null;

	constructor(
		private router: Router,
		private store: Store<AppState>,
		private modalService: NgbModal,
		private actions$: Actions,
		private urlHistory: UrlHistoryService
	) {
		this.suspendSaleHeaders$ = this.store.select(
			suspendSaleSelectors.selectSuspendSaleHeaders
		);
	}

	cart$: Observable<CartItem[]>;

	reasons$: Observable<Map<string, string[]>>;

	reasons: Map<string, string[]>;

	cart: CartItem[];

	totalValueOfItems: number = 0;

	readyToProcess: boolean = false;

	subscribeToState() {
		this.cart$ = this.store.select(cartSelectors.cart);
		this.reasons$ = this.store.select(cartSelectors.reasons);
		this.reasons$.subscribe(v => {this.reasons = v});
		this.cart$.subscribe((cart) => {
			this.cart = cart;
			console.log("Cart:", cart);
			this.totalValueOfItems = cart.reduce(
				(total, item) => total + item.bestValue * item.quantity,
				0
			);
			this.readyToProcess = cart.length > 0;
		});

		this.store.select(cartSelectors.noItems).subscribe((n) => {
			this.readyToProcess = n > 0;
			console.log("Ready to process:", this.readyToProcess);
		});

		this.store.select(cartSelectors.total).subscribe((total) => {
			this.totalValueOfItems = total;
			console.log("Total value of items:", total);
		});
	}

	getReasons(item: StockItemDto){
		let v = this.reasons.get(item.barcode);
		return v == undefined ? [] : v;
	}

	itemLookup(item: StockItemDto) {
		this.addCartItem(item);
	}

	onTableItemDeleteClicked(itemIndex: number) {
		console.log("SALES COMPONENT: onTableItemDeleteClicked called with index:", itemIndex);
		if (!this.canEditCart) {
			CreateErrorModal(
				this.modalService,
				false,
				"Cannot modify cart for customer special orders."
			);
			return;
		}
		let cartItem = this.cart[itemIndex];
		console.log("SALES COMPONENT: Dispatching removeItem for cart item:", cartItem);
		this.store.dispatch(cartActions.removeItem({ id: cartItem.id, stockItem: cartItem.stockItem }));
		this.store.dispatch(cartActions.removeAllReasons({barcode: cartItem.stockItem.barcode}))
	}

	addCartItem(item: StockItemDto) {
		if (!this.canEditCart) {
			CreateErrorModal(
				this.modalService,
				false,
				"Cannot modify cart for customer special orders."
			);
			return;
		}
		this.store.dispatch(cartActions.addItem({ stockItem: item }));
	}

	initState() {
		this.store.dispatch(cartActions.init());
		this.store.dispatch(transActions.init());
	}

	ngOnInit() {
		if (this.urlHistory.previousUrl === '/customer-specials') {
			this.canEditCart = false;
		}

		let navState: { amount?: number; suspendNo?: number } = {};

		// Try to get the navigation state from the router
		const navigation = this.router.getCurrentNavigation();
		if (navigation && navigation.extras && navigation.extras.state) {
			navState = navigation.extras.state as { amount?: number; suspendNo?: number };
		} else if (window.history && window.history.state) {
			// Fallback to window.history.state if navigation is null
			navState = window.history.state as { amount?: number; suspendNo?: number };
		}

		if (this.urlHistory.previousUrl == "/home") this.initState();

		this.subscribeToState();
		if (navState && navState.suspendNo) {
			console.log(1);
			var amount = navState.amount;
			// this.store.dispatch(SuspendSaleActions.selectSuspendSaleSuccess({ lines: navState.suspendLines }));
			this.store.dispatch(SuspendSaleActions.selectSuspendSale({ suspendNo: navState.suspendNo }));

			this.router.navigate(["sales/payment"], {
				state: { amount }
			});
		}

		else {
			console.log("loading suspendhdrs");
			this.store.dispatch(SuspendSaleActions.loadSuspendSaleHeaders());

			this.subscription = this.suspendSaleHeaders$.subscribe((headers) => {
				console.log("Headers: ", headers);
				if (headers === undefined || this.modalOpen) return;
				if (headers.length > 0 && this.cart.length === 0) {
					this.modalOpen = true;
					const modalRef = this.modalService.open(
						SaleSuspendHdrModalComponent,
						{ size: "lg", centered: true }
					);
					modalRef.componentInstance.suspendSaleItems = headers;

					// Reset the flag when modal is closed
					modalRef.result.finally(() => {
						this.modalOpen = false;
					});
				}
			});

			this.subscription.add(
				this.actions$.pipe(
					ofType(SuspendSaleActions.selectSuspendSaleSuccess)
				).subscribe(() => {
					this.store.select(suspendSaleSelectors.selectCurrentSuspendSaleNo)
						.subscribe(suspendNo => {
							this.currentSuspendSaleNo = suspendNo;
							console.log('Current suspend sale number:', suspendNo);
						});
				})
			);
		}
	}

	ngOnDestroy() {
		if (this.subscription) {
			this.subscription.unsubscribe();
		}
	}

	attemptNext() {
		if (this.readyToProcess) {
			this.router.navigateByUrl("sales/payment");
		} else {
			CreateErrorModal(
				this.modalService,
				false,
				"You will need to process at least one item first."
			);
		}
	}

	openSundryModal() {
		this.modalService.open(SundryModalComponent);
	}
}

export function CartItemFromDto(dto: StockItemDto): CartItem {
	return {
		stockItem: dto,
		quantity: 1,
		bestValue: 0,
	} as CartItem;
}
