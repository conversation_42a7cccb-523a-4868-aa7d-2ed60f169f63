import { Component, OnInit, Input } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CartItem } from '../reducers/sales/cart/cart.reducer';
import { TransactionLineAdjustModalComponent } from '../transaction-line-adjust-modal/transaction-line-adjust-modal.component';
import * as cartActions from '../reducers/sales/cart/cart.actions';
import { CreateErrorModal } from "../error-modal/error-modal.component";

@Component({
  selector: 'pos-transaction-table',
  templateUrl: './transaction-table.component.html',
  styleUrls: ['./transaction-table.component.scss']
})
export class TransactionTableComponent implements OnInit {
  cart$: Observable<CartItem[]>;
  @Input() canEditCart: boolean = true;

  constructor(
    private store: Store<any>,
    private modalService: NgbModal
  ) {
    this.cart$ = this.store.select(state => state.cart.items);
  }

  ngOnInit() { }

  getReasons(stockItem: any): string[] {
    return [];
  }

  onTableItemClicked(item: CartItem) {
    if (!this.canEditCart) {
      CreateErrorModal(
        this.modalService,
        false,
        "Cannot modify cart."
      );
      return;
    }

    const modalRef = this.modalService.open(TransactionLineAdjustModalComponent);
    modalRef.componentInstance.stockItem = item.stockItem;
    modalRef.componentInstance.quantity = item.quantity;
    modalRef.componentInstance.originalPrice = item.originalPrice;
    modalRef.componentInstance.discountReason = item.discountReason;
    modalRef.componentInstance.isRefundAdjust = item.quantity < 0;

    modalRef.result.then(
      (result) => {
        if (result) {
          if (result.quantity !== item.quantity) {
            this.store.dispatch(cartActions.setNumberOfItems({
              id: item.id,
              stockItem: item.stockItem,
              quantity: result.quantity
            }));
          }
          if (result.price !== item.stockItem.price) {
            this.store.dispatch(cartActions.updateItemPrice({
              id: item.id,
              stockItem: item.stockItem,
              newPrice: result.price,
              reason: result.reason,
              discountCode: result.discountCode,
              discountPercent: result.discountPercent
            }));
          }
        }
      },
      (reason) => {
        console.log('Modal dismissed');
      }
    );
  }

  onTableItemDeleteClicked(index: number) {
    console.log("TRANSACTION TABLE: onTableItemDeleteClicked called with index:", index);
    if (!this.canEditCart) {
      CreateErrorModal(
        this.modalService,
        false,
        "Cannot modify cart for customer special orders."
      );
      return;
    }

    this.cart$.subscribe(items => {
      if (items && items[index] && items[index].stockItem) {
        const cartItem = items[index];
        console.log("TRANSACTION TABLE: Dispatching removeItem for cart item:", cartItem);
        this.store.dispatch(cartActions.removeItem({ id: cartItem.id, stockItem: cartItem.stockItem }));
      }
    }).unsubscribe();
  }

  onTableReasonDeleteClicked(itemIndex: number, reasonIndex: number) {
    this.cart$.subscribe(items => {
      if (items && items[itemIndex] && items[itemIndex].stockItem) {
        const cartItem = items[itemIndex];
        this.store.dispatch(cartActions.removeReason({
          barcode: cartItem.stockItem.barcode,
          reasonId: reasonIndex
        }));
      }
    }).unsubscribe();
  }

  removeDiscount(item: CartItem) {
    if (!this.canEditCart) {
      CreateErrorModal(
        this.modalService,
        false,
        "Cannot modify cart for customer special orders."
      );
      return;
    }

    this.store.dispatch(cartActions.removeDiscount({ id: item.id, stockItem: item.stockItem }));
  }

  removeAllDiscounts() {
    if (!this.canEditCart) {
      CreateErrorModal(
        this.modalService,
        false,
        "Cannot modify cart for customer special orders."
      );
      return;
    }

    this.store.dispatch(cartActions.removeAllDiscounts());
  }

  openAdjustModal(item: CartItem) {
    if (!this.canEditCart) {
      CreateErrorModal(
        this.modalService,
        false,
        "Cannot modify cart for customer special orders."
      );
      return;
    }

    const modalRef = this.modalService.open(TransactionLineAdjustModalComponent, {
      backdrop: 'static',
      keyboard: false
    });

    modalRef.componentInstance.stockItem = item.stockItem;
    modalRef.componentInstance.quantity = item.quantity;
    modalRef.componentInstance.originalPrice = item.originalPrice;
    modalRef.componentInstance.discountReason = item.discountReason;
    modalRef.componentInstance.discountPercent = item.discountPercent;
    modalRef.componentInstance.discountAmount = item.discount;
    modalRef.componentInstance.currentPrice = item.bestValue;

  }
}
