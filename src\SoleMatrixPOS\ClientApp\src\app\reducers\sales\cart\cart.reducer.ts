import { createReducer, on, Action, State } from "@ngrx/store";
import * as cartActions from "./cart.actions";
import {
	CartItemDto,
	StockItemDto,
	TranslogDto,
} from "../../../pos-server.generated";
import { total } from "./cart.selectors";

export class CartItem {
	id: string;
	quantity: number;
	stockItem: StockItemDto;
	bestValue: number;
	originalPrice: number;
	discount?: number;
	discountReason?: string;
	discountCode?: string;
	discountPercent?: number;
	lineNo: number;
}

export function cartItemToTranslog(
	item: CartItem,
	lineNumber: number,
	transNo: number,
	clientCode?: string,
): TranslogDto {
	console.log("CartItem for translog:", {
		discountCode: item.discountCode,
		parsedGST: item.discountCode ? parseFloat(item.discountCode) : null,
		discount: item.discount,
		originalPrice: item.originalPrice,
		bestValue: item.bestValue,
	});

	return {
		colourCode: item.stockItem.colourCode,
		styleCode: item.stockItem.styleCode,
		sizeCode: item.stockItem.size,
		quantity: item.quantity,
		sellingPrice: item.bestValue,
		lineNo: lineNumber,
		clientCode: clientCode,
		gst: item.discount ? parseFloat(item.discountCode) : 0,
		nettSelling: item.discount ? item.originalPrice : 0,
		transNo: transNo,
		stockDescription: item.stockItem.styleDescription,
		colourDescription: item.stockItem.colourName,
	} as TranslogDto;
	// edit: added stockDescription and colourDescription to the TranslogDto for prints to maintain that info
}

export class CartState {
	items: CartItem[];
	total: number;
	reasons: Map<string, string[]>;
	isExchangeMode: boolean;
}

export const initialState: CartState = {
	items: [],
	total: 0,
	reasons: new Map<string, string[]>(),
	isExchangeMode: false,
} as CartState;

function recalculateTotal(items: CartItem[]): number {
	return items.reduce((total, item) => {
		const itemPrice = item.bestValue !== undefined && item.bestValue !== null 
			? item.bestValue 
			: item.originalPrice;
		const lineTotal = itemPrice * item.quantity;
		return total + lineTotal;
	}, 0);
}

function compareStockItemDtos(
	first: StockItemDto | CartItemDto,
	second: StockItemDto | CartItemDto
) {
	return first.barcode == second.barcode;
}

// Helper to generate unique line identifiers (no external dependency)
function generateId(): string {
	return Math.random().toString(36).substr(2, 9);
}

/**
 * Determine whether the given cart item matches the incoming action.
 * Uses id when provided; falls back to barcode comparison for backward compatibility.
 */
function matchesActionItem(item: CartItem, action: { id?: string; stockItem?: StockItemDto }): boolean {
	if (action.id !== undefined && action.id !== null) {
		const matches = item.id === action.id;
		console.log(`Matching by ID: item.id=${item.id}, action.id=${action.id}, matches=${matches}`);
		return matches;
	}
	if (action.stockItem) {
		const matches = compareStockItemDtos(item.stockItem, action.stockItem);
		console.log(`Matching by barcode: item.barcode=${item.stockItem.barcode}, action.barcode=${action.stockItem.barcode}, matches=${matches}`);
		return matches;
	}
	console.log("No matching criteria provided");
	return false;
}

export const reducer = createReducer(
	initialState,
	on(cartActions.init, (state) => initialState),

	on(cartActions.addItemResponse, (state, action) => {
		const tmpItems = [...state.items];

		const price =
			action.cartItem.value != null && action.cartItem.value > 0
				? action.cartItem.value
				: action.cartItem.price;

		tmpItems.push({
			id: generateId(),
			stockItem: {
				barcode: action.cartItem.barcode,
				rrp: action.cartItem.rrp,
				styleCode: action.cartItem.styleCode,
				styleDescription: action.cartItem.styleDescription,
				makerCode: action.cartItem.makerCode,
				labelCode: action.cartItem.labelCode,
				colourCode: action.cartItem.colourCode,
				departmentCode: action.cartItem.departmentCode,
				colourName: action.cartItem.colourName,
				size: action.cartItem.size,
				price: price,
			},
			quantity: 1,
			bestValue: price,
			originalPrice: price,
		} as CartItem);
		

		return {
			...state,
			items: tmpItems,
			total: recalculateTotal(tmpItems),
		};
	}),
	on(cartActions.addReturnItem, (state, action) => {
		const tmpItems = [...state.items];
		
		const price = action.stockItem.price || 0;

		tmpItems.push({
			id: generateId(),
			stockItem: action.stockItem,
			quantity: -1,
			bestValue: price,
			originalPrice: price,
		} as CartItem);

		return {
			...state,
			items: tmpItems,
			total: recalculateTotal(tmpItems),
		};
	}),
	on(cartActions.removeItem, (state, action) => {
		console.log("REMOVE ITEM", action);
		console.log("Current cart items before removal:", state.items.map(item => ({ id: item.id, barcode: item.stockItem.barcode })));

		// Find the index of the first item that matches the action
		const indexToRemove = state.items.findIndex(item => matchesActionItem(item, action));
		console.log("Index to remove:", indexToRemove);

		if (indexToRemove === -1) {
			// No matching item found, return state unchanged
			console.log("No matching item found");
			return state;
		}

		// Create new array without the matched item
		const tmpItems = [
			...state.items.slice(0, indexToRemove),
			...state.items.slice(indexToRemove + 1)
		];

		console.log("Cart items after removal:", tmpItems.map(item => ({ id: item.id, barcode: item.stockItem.barcode })));

		return {
			...state,
			items: tmpItems,
			total: recalculateTotal(tmpItems),
		};
	}),

	on(cartActions.setNumberOfItems, (state, action) => {
		let tmpItems = Object.assign([], state.items);

		console.log("SET NUM ITEM", action);
		tmpItems.forEach((el: CartItem, i) => {
			console.log("cartActions.setNumberOfItems");
			if (matchesActionItem(el, action)) {
				console.log("!!!!!!!!!!!!!");
				let tmpBestValue = tmpItems[i].bestValue;
				let tmpDiscount = tmpItems[i].discount;
				let tmpOriginalPrice = tmpItems[i].originalPrice;
				let tmpDiscountReason = tmpItems[i].discountReason;
				let tmpDiscountCode = tmpItems[i].discountCode;
				tmpItems.splice(i, 1, {
					stockItem: action.stockItem,
					quantity: action.quantity,
					bestValue: tmpBestValue,
					originalPrice: tmpOriginalPrice,
					discount: tmpDiscount,
					discountReason: tmpDiscountReason,
					discountCode: tmpDiscountCode,
					id: tmpItems[i].id,
				} as CartItem);
			}
		});

		return {
			...state,
			items: tmpItems,
			total: recalculateTotal(tmpItems),
		};
	}),
	on(cartActions.addReason, (state, action) => {
		// Copy map (to conform to immutable state)
		let copyReasons = deepCopyReasonMap(state.reasons);
		// If reasons already exists, append to list
		// Otherwise, create a new list
		if (copyReasons.get(action.barcode)) {
			copyReasons.get(action.barcode).push(action.reason);
		} else {
			copyReasons.set(action.barcode, [action.reason]);
		}

		// Return the modified state
		console.log("Added: ", copyReasons);
		return { ...state, reasons: copyReasons };
	}),
	on(cartActions.removeReason, (state, action) => {
		let mapCopy = deepCopyReasonMap(state.reasons);
		mapCopy.get(action.barcode).splice(action.reasonId, 1);

		if (mapCopy.get(action.barcode).length == 0)
			mapCopy.delete(action.barcode);

		return { ...state, reasons: mapCopy };
	}),
	on(cartActions.removeAllReasons, (state, action) => {
		let mapCopy = deepCopyReasonMap(state.reasons);
		mapCopy.delete(action.barcode);
		return { ...state, reasons: mapCopy };
	}),
	on(cartActions.negateCart, (state, action) => {
		const negatedItems = state.items.map((item) => ({
			...item,
			quantity: -item.quantity,
		}));
		return {
			...state,
			items: negatedItems,
			total: recalculateTotal(negatedItems),
		};
	}),
	on(
		cartActions.updateItemPrice,
		(
			state,
			{ id, stockItem, newPrice, reason, discountPercent, discountCode }
		) => {
			const updatedItems = state.items.map((item) => {
				if (matchesActionItem(item, { id, stockItem })) {
					const discount =
						Math.round((item.originalPrice - newPrice) * 100) / 100;
					return {
						...item,
						bestValue: newPrice,
						discount: discount != 0 ? discount : 0,
						discountReason: discount != 0 ? reason : null,
						discountCode: discount != 0 ? discountCode : null,
						discountPercent: discountPercent,
						id: item.id,
					};
				}
				return item;
			});

			return {
				...state,
				items: updatedItems,
				total: recalculateTotal(updatedItems),
			};
		}
	),
	on(cartActions.removeDiscount, (state, { id, stockItem }) => {
		const updatedItems = state.items.map((item) => {
			if (matchesActionItem(item, { id, stockItem })) {
				return {
					...item,
					stockItem: {
						...item.stockItem,
						price: item.originalPrice, // Reset price to original
					},
					bestValue: item.originalPrice,
					discountCode: null,
					discountReason: null,
					discount: 0, // Remove the discount
					id: item.id,
				};
			}
			return item;
		});

		return {
			...state,
			items: updatedItems,
			total: recalculateTotal(updatedItems),
		};
	}),
	on(cartActions.removeAllDiscounts, (state) => {
		const updatedItems = state.items.map((item) => ({
			...item,
			bestValue: item.originalPrice,
			discount: 0,
			discountReason: null,
			discountPercent: null,
			discountCode: null,
			id: item.id,
		}));

		const newTotal = recalculateTotal(updatedItems);

		return {
			...state,
			items: updatedItems,
			total: newTotal,
		};
	}),
	on(
		cartActions.applyDiscountToAll,
		(state, { discountPercent, reason, discountCode }) => {
			const updatedItems = state.items.map((item) => {
				const newPrice =
					item.originalPrice * (1 - discountPercent / 100);
				const roundedPrice = Math.round(newPrice * 100) / 100;
				const discount =
					Math.round((item.originalPrice - roundedPrice) * 100) / 100;

				return {
					...item,
					bestValue: roundedPrice,
					discount: discount,
					discountReason: reason,
					discountPercent: discountPercent,
					discountCode: discountCode,
					id: item.id,
				};
			});

			const newTotal = recalculateTotal(updatedItems);

			return {
				...state,
				items: updatedItems,
				total: newTotal,
			};
		}
	),
	on(
		cartActions.applyAmountToAll,
		(state, { discountAmount, reason, discountCode }) => {
			const updatedItems = state.items.map((item) => {
				const discountPercent =
					(discountAmount / item.originalPrice) * 100;
				const newPrice = item.originalPrice - discountAmount;
				const roundedPrice = Math.max(
					Math.round(newPrice * 100) / 100,
					0.01
				);

				return {
					...item,
					bestValue: roundedPrice,
					discount: discountAmount,
					discountReason: reason,
					discountPercent: Math.round(discountPercent * 100) / 100,
					discountCode: discountCode,
					id: item.id,
				};
			});

			return {
				...state,
				items: updatedItems,
				total: recalculateTotal(updatedItems),
			};
		}
	),
	on(cartActions.addSuspendedSaleItem, (state, action) => {
		let tmpItems = Object.assign([], state.items);
		let tmpReasons = deepCopyReasonMap(state.reasons);

		// Since SuspendTransToCartDto matches CartItem structure, we can map it directly
		const newItem: CartItem = {
			quantity: action.cartItem.quantity || 1,
			stockItem: action.cartItem.stockItem,
			bestValue: action.cartItem.bestValue,
			originalPrice: action.cartItem.originalPrice,
			discount: action.cartItem.discount,
			discountReason: action.cartItem.discountReason,
			discountCode: action.cartItem.discountCode,
			discountPercent: action.cartItem.discountPercent,
			lineNo: action.cartItem.lineNo,
			id: (action.cartItem as any).id ? (action.cartItem as any).id : generateId(),
		} as CartItem;

		tmpItems.push(newItem);

		// Only add to reasons map if reasonCode exists and is not empty
		if (action.cartItem.discountCode &&
			action.cartItem.discountCode.trim() !== '' &&
			action.cartItem.discountReason) {
			tmpReasons.set(action.cartItem.stockItem.barcode, [action.cartItem.discountReason]);
		}

		return {
			...state,
			items: tmpItems,
			reasons: tmpReasons,
			total: recalculateTotal(tmpItems),
		};
	}),
	on(cartActions.addSundryItem, (state, { price }) => {
		let tmpItems = Object.assign([], state.items);

		const sundryItem: CartItem = {
			stockItem: {
				barcode: '0',
				styleCode: 'SUNDRIES',
				colourCode: 'AST',
				departmentCode: 'AST',
				colourName: 'ASSORTED',
				styleDescription: 'ASSORTED',
				size: 'AST',
				price: price,
				value: price,
			} as StockItemDto,
			quantity: 1,
			bestValue: price,
			originalPrice: price,
			id: generateId(),
		} as CartItem;

		tmpItems.push(sundryItem);

		return {
			...state,
			items: tmpItems,
			total: recalculateTotal(tmpItems),
		};
	}),
	on(cartActions.setExchangeMode, (state, { isExchangeMode }) => ({
		...state,
		isExchangeMode
	})),
	on(cartActions.addSundryItemWithReason, (state, { price, description }) => {
		console.log("Handling addSundryItemWithReason action:", price, description);
		
		let tmpItems = Object.assign([], state.items);
		let tmpReasons = deepCopyReasonMap(state.reasons);

		const lineNo = tmpItems.length + 1;
		
		// unique barcode for the pupose of creating a unique sundry item, and deleting reasons are based on barcode. so we need to create a unique barcode for each sundry item.
		const uniqueBarcode = `SUN${Math.floor(Math.random() * 1000000000)}`;

		console.log("Creating sundry item with barcode:", uniqueBarcode);

		const sundryItem: CartItem = {
			stockItem: {
				barcode: uniqueBarcode,
				styleCode: 'SUNDRIES',
				colourCode: 'AST',
				departmentCode: 'AST',
				colourName: 'ASSORTED',
				styleDescription: description || 'SUNDRY ITEM',
				size: 'AST',
				price: price,
				value: price,
			} as StockItemDto,
			quantity: 1,
			bestValue: price,
			originalPrice: price,
			lineNo: lineNo,
			id: generateId(),
		} as CartItem;

		tmpItems.push(sundryItem);
		
		if (description && description.trim() !== '') {
			console.log("Adding reason for barcode:", uniqueBarcode, "reason:", description);
			tmpReasons.set(uniqueBarcode, [description]);
		}

		console.log("Updated cart items:", tmpItems);
		console.log("Updated reasons:", tmpReasons);

		return {
			...state,
			items: tmpItems,
			reasons: tmpReasons,
			total: recalculateTotal(tmpItems),
		};
	})
);

/**
 * Create a deep copy of the map.
 * Note that this should only be used for the map of the specified type,
 * due to the specifics when copying.
 * @param reasonMap Map to be copied, as used in `ReturnsState`
 * @returns A deep copy of the map
 */
function deepCopyReasonMap(
	reasonMap: Map<string, string[]>
): Map<string, string[]> {
	let mapCopy = new Map<string, string[]>();
	for (let key of reasonMap.keys()) {
		mapCopy.set(key, [...reasonMap.get(key)]);
	}
	return mapCopy;
}
