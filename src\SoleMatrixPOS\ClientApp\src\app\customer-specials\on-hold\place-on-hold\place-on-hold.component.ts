import { Component, OnInit, OnDestroy } from '@angular/core';
import { select, Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import Swal from 'sweetalert2';
import * as receiptActions from 'src/app/reducers/receipt-printing/receipt.actions';
import { StockItemDto, CustomerClubDto, CreateOrderDto, CustOrderHeaderDTO, CustOrderLineDTO, TransactionDto, TranslogDto, CreateOrderRequest, ReceiptTransactionDto } from 'src/app/pos-server.generated';
import * as cartActions from 'src/app/reducers/sales/cart/cart.actions';
import * as cartSelectors from 'src/app/reducers/sales/cart/cart.selectors';
import { Payment, PaymentType, Transaction } from 'src/app/payment/payment.service';
import * as transActions from 'src/app/reducers/transaction/transaction.actions';
import { cartItemToTranslog } from 'src/app/reducers/sales/cart/cart.reducer';
import * as SysConfigActions from 'src/app/reducers/sys-config/sys-config.actions';
import * as SysConfigSelectors from 'src/app/reducers/sys-config/sys-config.selectors';
import * as customerClubSearchSelectors from 'src/app/reducers/customer-club/club-search/customer-club.selectors';
import * as orderActions from 'src/app/reducers/order-item/order.actions';
import { CartItem } from 'src/app/reducers/sales/cart/cart.reducer';
import { AppState } from 'src/app/reducers';
import { OnHoldDepositModalComponent } from '../on-hold-deposit-modal/on-hold-deposit-modal.component';
import { UrlHistoryService } from 'src/app/url-history.service';
import * as transSelectors from 'src/app/reducers/transaction/transaction.selectors';
import { tap } from 'rxjs/operators';
import { EmailReceiptComponent } from 'src/app/email-receipt/email-receipt.component';

@Component({
  selector: 'pos-place-on-hold',
  templateUrl: './place-on-hold.component.html',
  styleUrls: ['./place-on-hold.component.scss']
})
export class PlaceOnHoldComponent implements OnInit {

  selectedCustomerClubMember$: Observable<CustomerClubDto>;
  transaction: Transaction; // Transaction to handle payments
  transaction$: Observable<Transaction>;
  cart$: Observable<CartItem[]>;
  cart: CartItem[] = [];
  selectedCustomerClubMember: CustomerClubDto = null;
  totalValueOfItems: number = 0;
  transNo: number = 0;
  readyToProcess: boolean = false;
  depositRequired: boolean = true; // New variable to indicate if deposit is required
  depositAmount: number = 0; // The deposit amount
  depositPercent: number = 0;

  private subscriptions: Subscription = new Subscription();

  constructor(
    private router: Router,
    private store: Store<AppState>,
    private modalService: NgbModal,
    private urlHistory: UrlHistoryService
  ) {
    this.transaction = new Transaction(0);
   }

  ngOnInit() {
        this.store.dispatch(transActions.getTransactionNo());
    this.store.select(transSelectors.transNo)
      .pipe(
        tap(transNo => {
          this.transNo = transNo;
          console.log('Transaction number updated:', transNo);
        })
      )
      .subscribe();

    this.store.dispatch(cartActions.init()); 

    if (this.urlHistory.previousUrl === '/home') this.initState();

    this.selectedCustomerClubMember$ = this.store.select(customerClubSearchSelectors.selectedCustomerClubMember);
    const customerSub = this.selectedCustomerClubMember$.subscribe(
      (customerDetails) => {
        if (customerDetails) {
          console.log('Customer details received:', customerDetails);
          this.selectedCustomerClubMember = customerDetails;
        }
      }
    );
    this.subscriptions.add(customerSub);

    const orderDepositSub = this.store.select(SysConfigSelectors.selectOnHoldDeposit).subscribe(
      (orderDeposit) => {
        console.log('Order deposit retrieved from store:', orderDeposit);
        this.depositPercent = orderDeposit; // Update depositPercent variable
      }
    );

    this.subscribeToState();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  subscribeToState() {
    this.cart$ = this.store.select(cartSelectors.cart);
    const cartSub = this.cart$.subscribe(cart => {
      this.cart = cart;
      this.calculateTotalValue();
      this.calculateDeposit();
    });
    this.subscriptions.add(cartSub);

    const readySub = this.store.select(cartSelectors.noItems).subscribe(n => this.readyToProcess = (n > 0));
    this.subscriptions.add(readySub);
  }

  initState() {
    this.store.dispatch(cartActions.init());
    this.store.dispatch(transActions.init());
  }

  backBtnClick() {
    console.log("Navigating backwards...");
    this.router.navigateByUrl(this.urlHistory.previousUrl);
  }

  itemLookup(item: StockItemDto) {
    this.addCartItem(item);
  }

  addCartItem(item: StockItemDto) {
    this.store.dispatch(cartActions.addItem({ stockItem: item }));
    console.log(item);
  }

  onTableItemDeleteClicked(itemIndex: number) {
    let cartItem = this.cart[itemIndex];
    this.store.dispatch(cartActions.removeItem({ id: cartItem.id, stockItem: cartItem.stockItem }));
  }

  attemptNext() {
    if (this.readyToProcess) {
      this.router.navigateByUrl("sales/payment");
    } else {
      Swal.fire("Error", "You will need to process at least one item first.", 'error');
    }
  }

  // Implement orderItemClick method
  orderItemClick() {
    if (!this.readyToProcess) {
      Swal.fire("Error", "You will need to add at least one item to the cart before ordering.", 'error');
      return;
    }
    // Instead of waiting for transNo, directly proceed with order creation
    const orderHeader = this.createOrderHeader();
    const orderLines = this.createOrderLines();

    this.store.dispatch(orderActions.setOrderHeader({ payload: orderHeader }));
    this.store.dispatch(orderActions.setOrderLines({ payload: orderLines }));
    this.openDepositModal();
  }

  openEmailModal(receiptTrans: ReceiptTransactionDto): Promise<void> {
    return new Promise((resolve) => {
      const modalRef = this.modalService.open(EmailReceiptComponent, {
      size: 'lg',
      backdrop: 'static'
      });
    
      // Pass receiptTrans to the EmailReceiptComponent
      modalRef.componentInstance.receiptTrans = receiptTrans;
    
      // Check if a customer club member is selected and pass the email
      if (this.selectedCustomerClubMember && this.selectedCustomerClubMember.email) {
      modalRef.componentInstance.customerEmail = this.selectedCustomerClubMember.email;
      }
    
      modalRef.result.then(() => {
      console.log('Email receipt sent.');
      resolve();  // Resolve the promise once the modal is closed
      }).catch(() => {
      resolve();  // Resolve the promise if the modal is dismissed
      });
    });
    }

  // Helper method to create OrderHeader
  private createOrderHeader(): CustOrderHeaderDTO {
    const clientCode = this.selectedCustomerClubMember && this.selectedCustomerClubMember.clientCode ? this.selectedCustomerClubMember.clientCode : "NOCODE";
    const orderStaff = '1'; // Replace with actual staff ID

    const orderHeader: CustOrderHeaderDTO = {
      orderCode: null, // Server will generate this
      orderStaff: orderStaff,
      clientCode: clientCode,
      orderCreated: new Date(),
      orderPlacedDate: new Date(),
      eta: null,
      lastContactDate: new Date(),
      orderArrivalDate: new Date(),
      cancelledDate: null,
      customerCollectionDate: null,
      cancelled: false,
      orderPlaced: false,
    };

    return orderHeader;
  }

  // Helper method to create OrderLines
  private createOrderLines(): CustOrderLineDTO[] {
    const orderLines: CustOrderLineDTO[] = [];

    let lineNo = 1;
    for (const cartItem of this.cart) {
      const line: CustOrderLineDTO = {
        orderCode: null, // Will be set on the server side
        lineNo: lineNo,
        orderStyle: cartItem.stockItem.styleDescription,
        orderColour: cartItem.stockItem.colourName,
        orderSize: cartItem.stockItem.size,
        styleCode: cartItem.stockItem.styleCode,
        colourCode: cartItem.stockItem.colourCode,
        sizeCode: cartItem.stockItem.size,
        quantity: cartItem.quantity,
        sellingPrice: cartItem.bestValue,
        extendedValue: cartItem.bestValue * cartItem.quantity,
        transType: 11
      };

      orderLines.push(line);
      lineNo++;
    }

    return orderLines;
  }

  // Get the transaction details (stub for the example)
  getSaleTransaction(): TransactionDto {
    this.transaction = new Transaction(0);
    // Get all translogs
    let resTranslogs: TranslogDto[] = [];
    for (let i = 0; i < this.cart.length; i++) {
      let cartItem = this.cart[i];
      let lineNumber = i + 1;
      
      // Check if the customer club member exists and has a clientCode
      let clientCode = (this.selectedCustomerClubMember && this.selectedCustomerClubMember.clientCode)
        ? this.selectedCustomerClubMember.clientCode
        : undefined;
  
      resTranslogs.push(cartItemToTranslog(cartItem, lineNumber, this.transNo, clientCode));
    }
  
    // Create the base transaction object
    let transaction: TransactionDto = {
      payments: this.transaction.toTranspayDtos(),
      translogs: resTranslogs,
      transType: 11
    };
    console.log("the transaction", transaction)
    return transaction;
  }		

    // Calculate the total value of items in the cart
    private calculateTotalValue() {
      this.totalValueOfItems = this.cart.reduce((total, item) => total + (item.bestValue * item.quantity), 0);
    }
  
    // Calculate the deposit amount based on 10% of the total value of items, rounded up to the nearest whole number
    private calculateDeposit() {
      this.depositAmount = Math.ceil(this.totalValueOfItems * (0.01 * this.depositPercent));
    }

  // Method to open the deposit modal
  private openDepositModal() {
    const modalRef = this.modalService.open(OnHoldDepositModalComponent, {
      centered: true,
      size: 'lg'
    });
	  modalRef.componentInstance.depositAmount = this.depositAmount;  // Pass the rounded deposit amount to the modal
	  modalRef.componentInstance.amountDue = this.totalValueOfItems;
  }
}
